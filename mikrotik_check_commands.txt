# MikroTik 系统检查命令
# 请逐个执行这些命令，并把输出发给我

## 1. 系统基本信息
/system resource print
/system routerboard print

## 2. 容器功能检查
/container print
/container config print

## 3. 网络接口检查
/interface print
/ip address print

## 4. 挂载点检查
/container mounts print

## 5. 文件系统检查
/file print where name~"disk1"

## 6. 容器状态检查
/container print detail

## 7. 网络连通性测试
/ping ************* count=3
/ping ************* count=3
/ping ************* count=3

## 8. 端口检查
/tool netwatch print

## 9. 防火墙规则检查
/ip firewall filter print
/ip firewall nat print

## 10. 日志检查
/log print where topics~"container"

## 11. DNS 测试
/tool nslookup name=baidu.com server=*************
/tool nslookup name=google.com server=*************

## 12. 如果容器运行中，检查容器内部
/container shell 0
# 在容器内执行：
# ps aux
# netstat -ln
# exit
