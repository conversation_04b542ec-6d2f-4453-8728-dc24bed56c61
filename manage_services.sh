#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}MikroTik DNS 服务管理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     - 启动所有服务"
    echo "  stop      - 停止所有服务"
    echo "  restart   - 重启所有服务"
    echo "  status    - 查看服务状态"
    echo "  logs      - 查看服务日志"
    echo "  test      - 测试DNS服务"
    echo "  help      - 显示此帮助信息"
    echo ""
}

# 启动服务
start_services() {
    echo -e "${GREEN}启动 DNS 服务...${NC}"
    docker-compose up -d
    echo ""
    echo -e "${YELLOW}等待服务启动...${NC}"
    sleep 10
    check_status
}

# 停止服务
stop_services() {
    echo -e "${RED}停止 DNS 服务...${NC}"
    docker-compose down
}

# 重启服务
restart_services() {
    echo -e "${YELLOW}重启 DNS 服务...${NC}"
    docker-compose restart
    echo ""
    echo -e "${YELLOW}等待服务重启...${NC}"
    sleep 10
    check_status
}

# 检查服务状态
check_status() {
    echo -e "${BLUE}=== 服务状态检查 ===${NC}"
    docker-compose ps
    echo ""
    
    # 检查端口
    echo -e "${BLUE}=== 端口检查 ===${NC}"
    echo -n "MosDNS (5335): "
    if nc -z -w3 ************* 5335 2>/dev/null; then
        echo -e "${GREEN}✅ 正常${NC}"
    else
        echo -e "${RED}❌ 异常${NC}"
    fi
    
    echo -n "Clash (7890): "
    if nc -z -w3 ************* 7890 2>/dev/null; then
        echo -e "${GREEN}✅ 正常${NC}"
    else
        echo -e "${RED}❌ 异常${NC}"
    fi
    
    echo -n "AdGuard (53): "
    if nc -z -w3 ************* 53 2>/dev/null; then
        echo -e "${GREEN}✅ 正常${NC}"
    else
        echo -e "${RED}❌ 异常${NC}"
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}选择要查看的服务日志:${NC}"
    echo "1) MosDNS"
    echo "2) Clash"
    echo "3) AdGuard Home"
    echo "4) 所有服务"
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1) docker-compose logs -f mosdns ;;
        2) docker-compose logs -f clash ;;
        3) docker-compose logs -f adguardhome ;;
        4) docker-compose logs -f ;;
        *) echo -e "${RED}无效选择${NC}" ;;
    esac
}

# 测试DNS服务
test_dns() {
    echo -e "${BLUE}=== DNS 服务测试 ===${NC}"
    
    # 测试MosDNS
    echo -n "测试 MosDNS DNS 解析: "
    if nslookup baidu.com ************* 2>/dev/null | grep -q "Address:"; then
        echo -e "${GREEN}✅ 正常${NC}"
    else
        echo -e "${RED}❌ 异常${NC}"
    fi
    
    # 测试AdGuard
    echo -n "测试 AdGuard DNS 解析: "
    if nslookup baidu.com ************* 2>/dev/null | grep -q "Address:"; then
        echo -e "${GREEN}✅ 正常${NC}"
    else
        echo -e "${RED}❌ 异常${NC}"
    fi
    
    # 测试代理连接
    echo -n "测试 Clash 代理连接: "
    if curl -x http://*************:7890 -s --connect-timeout 5 http://www.google.com > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 正常${NC}"
    else
        echo -e "${RED}❌ 异常${NC}"
    fi
}

# 主程序
case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        check_status
        ;;
    logs)
        show_logs
        ;;
    test)
        test_dns
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}错误: 未知选项 '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
