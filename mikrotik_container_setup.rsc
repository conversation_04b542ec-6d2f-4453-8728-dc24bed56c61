# MikroTik RouterOS 容器配置脚本
# 适用于 RouterOS 7.x 版本

# 1. 创建网桥和VLAN接口（如果还没有的话）
/interface bridge add name=docker-bridge
/ip address add address=************/24 interface=docker-bridge

# 2. 启用容器功能
/container config set registry-url=https://registry-1.docker.io tmpdir=disk1/tmp

# 3. 创建挂载点目录
/container mounts add name=mosdns-config src-path=/disk1/config/mosdns dst-path=/etc/mosdns
/container mounts add name=clash-config src-path=/disk1/config/clash dst-path=/root/.config/clash
/container mounts add name=adguard-work src-path=/disk1/adguard/work dst-path=/opt/adguardhome/work
/container mounts add name=adguard-conf src-path=/disk1/adguard/conf dst-path=/opt/adguardhome/conf

# 4. 创建环境变量
/container envs add name=mosdns-env key=TZ value=Asia/Shanghai

# 5. 添加 MosDNS 容器
/container add remote-image=irinesistiana/mosdns:latest \
    interface=docker-bridge \
    root-dir=disk1/containers/mosdns \
    mounts=mosdns-config \
    envlist=mosdns-env \
    cmd="mosdns,start,-c,/etc/mosdns/config.yaml" \
    logging=yes

# 6. 添加 Clash 容器
/container add remote-image=dreamacro/clash-premium:latest \
    interface=docker-bridge \
    root-dir=disk1/containers/clash \
    mounts=clash-config \
    logging=yes

# 7. 添加 AdGuard Home 容器
/container add remote-image=adguard/adguardhome:latest \
    interface=docker-bridge \
    root-dir=disk1/containers/adguard \
    mounts=adguard-work,adguard-conf \
    logging=yes

# 8. 配置端口转发规则
/ip firewall nat add chain=dstnat protocol=tcp dst-port=5335 action=dst-nat to-addresses=************* to-ports=5335
/ip firewall nat add chain=dstnat protocol=udp dst-port=5335 action=dst-nat to-addresses=************* to-ports=5335
/ip firewall nat add chain=dstnat protocol=tcp dst-port=7890 action=dst-nat to-addresses=************0 to-ports=7890
/ip firewall nat add chain=dstnat protocol=tcp dst-port=53 action=dst-nat to-addresses=************* to-ports=53
/ip firewall nat add chain=dstnat protocol=udp dst-port=53 action=dst-nat to-addresses=************* to-ports=53

# 9. 启动容器
/container start 0
/container start 1  
/container start 2
