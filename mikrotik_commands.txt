# MikroTik RouterOS 容器部署命令
# 请按顺序执行以下命令

## 1. 准备工作 - 检查系统
/system resource print
/container print

## 2. 配置容器环境
/container config set registry-url=https://registry-1.docker.io tmpdir=disk1/tmp

## 3. 创建目录结构（在 Files 中手动创建或使用命令）
# 需要在 Files 中创建以下目录结构：
# /disk1/config/mosdns/
# /disk1/config/clash/
# /disk1/adguard/work/
# /disk1/adguard/conf/
# /disk1/containers/

## 4. 创建挂载点
/container mounts add name=mosdns-config src-path=/disk1/config/mosdns dst-path=/etc/mosdns
/container mounts add name=clash-config src-path=/disk1/config/clash dst-path=/root/.config/clash
/container mounts add name=adguard-work src-path=/disk1/adguard/work dst-path=/opt/adguardhome/work
/container mounts add name=adguard-conf src-path=/disk1/adguard/conf dst-path=/opt/adguardhome/conf

## 5. 创建网络接口（如果需要）
/interface bridge add name=docker-bridge
/ip address add address=************/24 interface=docker-bridge

## 6. 添加容器

### MosDNS 容器
/container add remote-image=irinesistiana/mosdns:latest interface=veth1 root-dir=disk1/containers/mosdns mounts=mosdns-config cmd="mosdns,start,-c,/etc/mosdns/config.yaml" logging=yes

### Clash 容器  
/container add remote-image=dreamacro/clash-premium:latest interface=veth2 root-dir=disk1/containers/clash mounts=clash-config logging=yes

### AdGuard Home 容器
/container add remote-image=adguard/adguardhome:latest interface=veth3 root-dir=disk1/containers/adguard mounts=adguard-work,adguard-conf logging=yes

## 7. 配置容器网络
/interface veth add name=veth1 address=*************/24 gateway=************
/interface veth add name=veth2 address=*************/24 gateway=************  
/interface veth add name=veth3 address=*************/24 gateway=************

## 8. 启动容器
/container start 0
/container start 1
/container start 2

## 9. 检查容器状态
/container print
/container shell 0

## 10. 测试命令
# 测试 MosDNS
/tool fetch url="http://*************:9091" keep-result=no

# 测试端口
/tool netwatch add host=************* interval=10s
/tool netwatch add host=************* interval=10s  
/tool netwatch add host=************* interval=10s

## 11. 防火墙规则（如果需要）
/ip firewall filter add chain=input protocol=tcp dst-port=5335 action=accept
/ip firewall filter add chain=input protocol=udp dst-port=5335 action=accept
/ip firewall filter add chain=input protocol=tcp dst-port=7890 action=accept
/ip firewall filter add chain=input protocol=tcp dst-port=53 action=accept
/ip firewall filter add chain=input protocol=udp dst-port=53 action=accept
