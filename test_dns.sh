#!/bin/bash

echo "=== DNS 服务测试脚本 ==="
echo "测试时间: $(date)"
echo ""

# 测试MosDNS端口
echo "1. 测试 MosDNS 端口 5335..."
if nc -z -v -w5 ************* 5335 2>/dev/null; then
    echo "✅ MosDNS 端口 5335 可达"
else
    echo "❌ MosDNS 端口 5335 不可达"
fi

# 测试Clash端口
echo ""
echo "2. 测试 Clash 端口 7890..."
if nc -z -v -w5 ************* 7890 2>/dev/null; then
    echo "✅ Clash 端口 7890 可达"
else
    echo "❌ Clash 端口 7890 不可达"
fi

# 测试AdGuard端口
echo ""
echo "3. 测试 AdGuard Home 端口 53..."
if nc -z -v -w5 ************* 53 2>/dev/null; then
    echo "✅ AdGuard Home 端口 53 可达"
else
    echo "❌ AdGuard Home 端口 53 不可达"
fi

# DNS解析测试
echo ""
echo "4. DNS 解析测试..."
echo "测试国内域名 (baidu.com):"
if nslookup baidu.com ************* 2>/dev/null | grep -q "Address:"; then
    echo "✅ 国内域名解析正常"
else
    echo "❌ 国内域名解析失败"
fi

echo ""
echo "测试国外域名 (google.com):"
if nslookup google.com ************* 2>/dev/null | grep -q "Address:"; then
    echo "✅ 国外域名解析正常"
else
    echo "❌ 国外域名解析失败"
fi

echo ""
echo "=== 测试完成 ==="
