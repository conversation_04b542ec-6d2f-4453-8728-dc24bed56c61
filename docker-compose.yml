version: '3.8'

services:
  # MosDNS - DNS分流服务
  mosdns:
    image: irinesistiana/mosdns:latest
    container_name: mosdns
    restart: unless-stopped
    ports:
      - "*************:5335:5335/udp"  # UDP DNS端口
      - "*************:5335:5335/tcp"  # TCP DNS端口
      - "*************:9091:9091/tcp"  # API管理端口
    volumes:
      - ./mosdns/config.yaml:/etc/mosdns/config.yaml:ro
      - ./mosdns/direct-list.txt:/etc/mosdns/direct-list.txt:ro
      - ./mosdns/proxy-list.txt:/etc/mosdns/proxy-list.txt:ro
      - ./mosdns/china_ip_list.txt:/etc/mosdns/china_ip_list.txt:ro
      - ./mosdns/mosdns.log:/etc/mosdns/mosdns.log
    command: ["mosdns", "start", "-c", "/etc/mosdns/config.yaml"]
    networks:
      dns_network:
        ipv4_address: *************

  # Clash - 代理服务
  clash:
    image: dreamacro/clash-premium:latest
    container_name: clash
    restart: unless-stopped
    ports:
      - "************0:7890:7890"      # HTTP/SOCKS5代理端口
      - "************0:9090:9090"      # Web UI端口
    volumes:
      - ./clash/config.yaml:/root/.config/clash/config.yaml:ro
    networks:
      dns_network:
        ipv4_address: ************0

  # AdGuard Home - 广告拦截和DNS服务
  adguardhome:
    image: adguard/adguardhome:latest
    container_name: adguardhome
    restart: unless-stopped
    ports:
      - "*************:53:53/tcp"      # DNS TCP
      - "*************:53:53/udp"      # DNS UDP
      - "*************:80:80/tcp"      # Web UI HTTP
      - "*************:443:443/tcp"    # Web UI HTTPS
      - "*************:3000:3000/tcp"  # 初始设置端口
    volumes:
      - adguard_work:/opt/adguardhome/work
      - adguard_conf:/opt/adguardhome/conf
    networks:
      dns_network:
        ipv4_address: *************

networks:
  dns_network:
    driver: bridge
    ipam:
      config:
        - subnet: ************/24
          gateway: ************

volumes:
  adguard_work:
  adguard_conf:
