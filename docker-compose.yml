version: '3.8'

services:
  # MosDNS - DNS分流服务
  mosdns:
    image: irinesistiana/mosdns:latest
    container_name: mosdns
    restart: unless-stopped
    network_mode: host
    volumes:
      - ./mosdns/config.yaml:/etc/mosdns/config.yaml:ro
      - ./mosdns/direct-list.txt:/etc/mosdns/direct-list.txt:ro
      - ./mosdns/proxy-list.txt:/etc/mosdns/proxy-list.txt:ro
      - ./mosdns/china_ip_list.txt:/etc/mosdns/china_ip_list.txt:ro
      - ./mosdns/mosdns.log:/etc/mosdns/mosdns.log
    command: ['mosdns', 'start', '-c', '/etc/mosdns/config.yaml']

  # Clash - 代理服务
  clash:
    image: dreamacro/clash-premium:latest
    container_name: clash
    restart: unless-stopped
    network_mode: host
    volumes:
      - ./clash/config.yaml:/root/.config/clash/config.yaml:ro

  # AdGuard Home - 广告拦截和DNS服务
  adguardhome:
    image: adguard/adguardhome:latest
    container_name: adguardhome
    restart: unless-stopped
    network_mode: host
    volumes:
      - adguard_work:/opt/adguardhome/work
      - adguard_conf:/opt/adguardhome/conf

volumes:
  adguard_work:
  adguard_conf:
